{"name": "charotte-server", "version": "1.0.0", "description": "Backend server for Charotte social media app", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["social media", "express", "mongodb", "socket.io"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "socket.io": "^4.7.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cloudinary": "^1.40.0", "multer-storage-cloudinary": "^4.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}}