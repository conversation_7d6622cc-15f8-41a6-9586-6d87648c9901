const User = require('../models/User');
const Char = require('../models/Char');

exports.getProfile = async (req, res) => {
  try {
    const user = await User.findOne({ username: req.params.username });
    if (!user) return res.status(404).json({ msg: 'User not found' });
    
    const chars = await Char.find({ author: user._id })
      .sort('-createdAt')
      .populate('author', 'username avatarUrl');
    
    res.json({ user, chars });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: 'Server error' });
  }
};
