import React from 'react';
import { Link } from 'react-router-dom';

const CommentList = ({ comments, loading }) => {
  if (loading) {
    return <div className="text-center py-4">Loading comments...</div>;
  }

  if (comments.length === 0) {
    return <div className="text-gray-500 text-center py-4">No comments yet</div>;
  }

  return (
    <div className="space-y-3">
      {comments.map((comment) => (
        <div key={comment._id} className="flex space-x-3">
          <div className="w-8 h-8 bg-primary-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
            {comment.author.username[0].toUpperCase()}
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <Link 
                to={`/profile/${comment.author.username}`}
                className="font-medium text-gray-900 hover:text-primary-600 text-sm"
              >
                @{comment.author.username}
              </Link>
              <span className="text-xs text-gray-500">
                {new Date(comment.createdAt).toLocaleDateString()}
              </span>
            </div>
            <p className="text-gray-700 text-sm mt-1">{comment.content}</p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CommentList;
