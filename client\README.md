# CHAROTTE Frontend

CHAROTTE is a MERN-based social media app blending features from Twitter (posts called "Chars") and TikTok (video sharing). This frontend is built with React and TailwindCSS.

## 📦 Features
- Real-time Chars feed with Socket.IO
- Post short text + optional video
- Like, repost, and comment on Chars
- User profile pages
- Responsive layout for mobile and desktop

## 🚀 Getting Started

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/charotte.git
cd charotte/client
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Setup Environment Variables
Create a `.env` file based on the provided example:
```bash
cp .env.example .env
```

Edit `.env` and set the API URL:
```env
REACT_APP_API_URL=http://localhost:5000/api
```

### 4. Start the Development Server
```bash
npm start
```

The app will run at [http://localhost:3000](http://localhost:3000)

## 🧪 Tech Stack
- React
- TailwindCSS
- Axios
- React Router
- Socket.IO Client

## 🛠 Project Structure
```
client/
├── public/
├── src/
│   ├── components/     # Reusable UI (CharCard, CommentList, etc)
│   ├── pages/          # Page components (HomePage, UploadPage, UserProfile)
│   ├── services/       # Socket.IO setup and API calls
│   ├── utils/          # Authentication utilities
│   ├── App.js
│   ├── index.js
│   └── index.css
├── .env.example
├── postcss.config.js
├── tailwind.config.js
└── README.md
```

## 🖼 Sample Screens
- Home feed with Chars
- Upload form with video file input
- Comments on each Char
- User profiles with past posts

---

### 📡 Connect to Backend
Make sure to run the CHAROTTE backend server (`cd server && npm start`) alongside this React app.

---

Happy Charring! 🚀
