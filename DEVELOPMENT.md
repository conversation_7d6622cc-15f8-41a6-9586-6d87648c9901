# Development Guide for Charotte

This guide helps developers get started with contributing to Charotte.

## 🛠 Development Setup

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- MongoDB (local or Atlas)
- Git

### Quick Start
```bash
# Clone the repository
git clone https://github.com/your-username/charotte.git
cd charotte

# Run setup script
npm run setup

# Start development servers
npm run dev
```

### Manual Setup
```bash
# Install root dependencies
npm install

# Install server dependencies
cd server && npm install

# Install client dependencies
cd ../client && npm install

# Copy environment files
cp server/.env.example server/.env
cp client/.env.example client/.env

# Edit environment files with your configuration
```

## 📁 Project Structure

```
charotte/
├── client/                 # React frontend
│   ├── public/            # Static files
│   ├── src/
│   │   ├── components/    # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API and Socket.IO
│   │   ├── utils/         # Helper functions
│   │   └── App.js         # Main app component
│   └── package.json
├── server/                # Node.js backend
│   ├── controllers/       # Route handlers
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   ├── middleware/       # Custom middleware
│   ├── utils/            # Utilities
│   └── index.js          # Server entry point
├── package.json          # Root package.json
├── setup.js              # Setup script
└── README.md
```

## 🔧 Available Scripts

### Root Level
- `npm run setup` - Initial project setup
- `npm run dev` - Start both client and server in development
- `npm run install:all` - Install all dependencies

### Server
- `npm run dev` - Start server with nodemon
- `npm start` - Start server in production mode

### Client
- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests

## 🎨 Code Style

### JavaScript/React
- Use ES6+ features
- Functional components with hooks
- Consistent naming conventions
- Proper error handling

### CSS/Styling
- TailwindCSS for styling
- Responsive design first
- Consistent spacing and colors

## 🧪 Testing

### Running Tests
```bash
# Client tests
cd client && npm test

# Server tests (when implemented)
cd server && npm test
```

### Writing Tests
- Use Jest and React Testing Library for client
- Use Jest and Supertest for server
- Write unit tests for utilities
- Write integration tests for API endpoints

## 🔍 Debugging

### Client Debugging
- Use React Developer Tools
- Console logging with proper levels
- Error boundaries for error handling

### Server Debugging
- Use `DEBUG=*` environment variable
- Console logging with timestamps
- Proper error handling and logging

### Database Debugging
- Use MongoDB Compass for GUI
- Check connection strings
- Monitor query performance

## 📡 API Development

### Adding New Endpoints
1. Create controller function
2. Add route definition
3. Add middleware if needed
4. Update API documentation
5. Test the endpoint

### Authentication
- All protected routes use JWT middleware
- Token format: `Bearer <token>`
- Token expires in 7 days

### Error Handling
```javascript
// Standard error response format
{
  "msg": "Error message",
  "error": "Detailed error (development only)"
}
```

## 🎯 Frontend Development

### Component Structure
```javascript
// Component template
import React, { useState, useEffect } from 'react';

const ComponentName = ({ prop1, prop2 }) => {
  const [state, setState] = useState(initialValue);

  useEffect(() => {
    // Side effects
  }, [dependencies]);

  const handleAction = () => {
    // Event handlers
  };

  return (
    <div className="tailwind-classes">
      {/* JSX content */}
    </div>
  );
};

export default ComponentName;
```

### State Management
- Use React hooks for local state
- Context API for global state (if needed)
- Avoid prop drilling

### API Calls
- Use axios for HTTP requests
- Centralized API service in `services/api.js`
- Proper error handling

## 🔄 Real-time Features

### Socket.IO Implementation
- Client connects on app load
- Server emits events for real-time updates
- Proper event cleanup on component unmount

### Events
- `char-added` - New char posted
- `char-liked` - Char liked/unliked
- `char-reposted` - Char reposted
- `comment-added` - New comment

## 🗄 Database

### MongoDB Schema Design
- User, Char, and Comment models
- Proper indexing for performance
- Reference vs embedded documents

### Migrations
- No formal migration system
- Document schema changes in commits
- Backward compatibility considerations

## 🚀 Performance

### Frontend Optimization
- Code splitting with React.lazy
- Image optimization
- Minimize bundle size

### Backend Optimization
- Database query optimization
- Proper indexing
- Caching strategies

## 🔒 Security

### Best Practices
- Input validation and sanitization
- SQL injection prevention (NoSQL injection)
- XSS protection
- CSRF protection
- Rate limiting

### Authentication Security
- Strong JWT secrets
- Password hashing with bcrypt
- Token expiration
- Secure cookie settings

## 📝 Contributing

### Git Workflow
1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

### Commit Messages
- Use conventional commits format
- Be descriptive but concise
- Reference issues when applicable

### Pull Request Process
1. Update documentation if needed
2. Add tests for new features
3. Ensure all tests pass
4. Request review from maintainers

## 🐛 Issue Reporting

### Bug Reports
- Use issue templates
- Provide reproduction steps
- Include environment details
- Add screenshots if applicable

### Feature Requests
- Describe the problem you're solving
- Propose a solution
- Consider implementation complexity

## 📚 Resources

### Documentation
- [React Documentation](https://reactjs.org/docs)
- [Express.js Guide](https://expressjs.com/en/guide)
- [MongoDB Manual](https://docs.mongodb.com/manual)
- [Socket.IO Documentation](https://socket.io/docs)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)

### Tools
- [MongoDB Compass](https://www.mongodb.com/products/compass)
- [Postman](https://www.postman.com/) for API testing
- [React Developer Tools](https://reactjs.org/blog/2019/08/15/new-react-devtools.html)

---

Happy coding! 🚀
