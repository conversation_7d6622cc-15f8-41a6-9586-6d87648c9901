// Application constants

export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
export const SOCKET_URL = process.env.REACT_APP_SOCKET_URL || 'http://localhost:5000';

// Character limits
export const CHAR_MAX_LENGTH = 280;
export const BIO_MAX_LENGTH = 160;

// File upload limits
export const MAX_VIDEO_SIZE = 50 * 1024 * 1024; // 50MB
export const ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/webm', 'video/ogg'];

// UI Constants
export const COLORS = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  }
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  AUTH_REQUIRED: 'Please log in to continue.',
  INVALID_CREDENTIALS: 'Invalid username or password.',
  USER_EXISTS: 'Username already exists.',
  CHAR_TOO_LONG: `Char must be ${CHAR_MAX_LENGTH} characters or less.`,
  VIDEO_TOO_LARGE: `Video must be ${MAX_VIDEO_SIZE / (1024 * 1024)}MB or less.`,
  INVALID_VIDEO_TYPE: 'Please upload a valid video file (MP4, WebM, or OGG).',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
};

// Success messages
export const SUCCESS_MESSAGES = {
  CHAR_POSTED: 'Char posted successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  COMMENT_ADDED: 'Comment added successfully!',
};

// Local storage keys
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER: 'user',
  THEME: 'theme',
};
