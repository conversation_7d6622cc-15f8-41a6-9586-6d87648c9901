import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import CommentList from './CommentList';

const CharCard = ({ char, currentUser, onUpdate }) => {
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [comments, setComments] = useState([]);
  const [loadingComments, setLoadingComments] = useState(false);

  const handleLike = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/chars/${char._id}/like`,
        {},
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      onUpdate(char._id, { likeCount: response.data.likeCount });
    } catch (error) {
      console.error('Error liking char:', error);
    }
  };

  const handleRepost = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/chars/${char._id}/repost`,
        {},
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      onUpdate(char._id, { repostCount: response.data.repostCount });
    } catch (error) {
      console.error('Error reposting char:', error);
    }
  };

  const loadComments = async () => {
    if (comments.length > 0) return;
    
    setLoadingComments(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/chars/${char._id}/comments`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setComments(response.data);
    } catch (error) {
      console.error('Error loading comments:', error);
    }
    setLoadingComments(false);
  };

  const handleAddComment = async (e) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/chars/${char._id}/comments`,
        { content: newComment },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      setComments([...comments, response.data]);
      setNewComment('');
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  const toggleComments = () => {
    setShowComments(!showComments);
    if (!showComments) {
      loadComments();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-4">
      <div className="flex items-center mb-4">
        <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold">
          {char.author.username[0].toUpperCase()}
        </div>
        <div className="ml-3">
          <Link 
            to={`/profile/${char.author.username}`}
            className="font-semibold text-gray-900 hover:text-primary-600"
          >
            @{char.author.username}
          </Link>
          <p className="text-sm text-gray-500">
            {new Date(char.createdAt).toLocaleDateString()}
          </p>
        </div>
      </div>

      {char.content && (
        <p className="text-gray-800 mb-4">{char.content}</p>
      )}

      {char.videoUrl && (
        <video 
          controls 
          className="w-full max-h-96 rounded-lg mb-4"
          src={char.videoUrl}
        />
      )}

      <div className="flex items-center space-x-6 text-gray-500">
        <button
          onClick={handleLike}
          className={`flex items-center space-x-1 hover:text-red-500 ${
            char.likedByMe ? 'text-red-500' : ''
          }`}
        >
          <span>❤️</span>
          <span>{char.likeCount || 0}</span>
        </button>

        <button
          onClick={handleRepost}
          className={`flex items-center space-x-1 hover:text-green-500 ${
            char.repostedByMe ? 'text-green-500' : ''
          }`}
        >
          <span>🔄</span>
          <span>{char.repostCount || 0}</span>
        </button>

        <button
          onClick={toggleComments}
          className="flex items-center space-x-1 hover:text-blue-500"
        >
          <span>💬</span>
          <span>Comments</span>
        </button>
      </div>

      {showComments && (
        <div className="mt-4 border-t pt-4">
          <form onSubmit={handleAddComment} className="mb-4">
            <div className="flex space-x-2">
              <input
                type="text"
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment..."
                className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <button
                type="submit"
                className="bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600"
              >
                Post
              </button>
            </div>
          </form>

          <CommentList 
            comments={comments} 
            loading={loadingComments}
            onNewComment={(comment) => setComments([...comments, comment])}
          />
        </div>
      )}
    </div>
  );
};

export default CharCard;
