const Comment = require('../models/Comment');

exports.addComment = async (req, res) => {
  try {
    const comment = new Comment({
      content: req.body.content,
      author: req.user.id,
      char: req.params.id
    });
    
    await comment.save();
    
    const populated = await comment.populate('author', 'username avatarUrl');
    
    req.app.get('io').emit('comment-added', { 
      charId: req.params.id, 
      comment: populated 
    });
    
    res.json(populated);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: 'Server error' });
  }
};

exports.getComments = async (req, res) => {
  try {
    const comments = await Comment.find({ char: req.params.id })
      .sort('createdAt')
      .populate('author', 'username avatarUrl');
    
    res.json(comments);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: 'Server error' });
  }
};
