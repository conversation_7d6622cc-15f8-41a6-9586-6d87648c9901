# CHAROTTE

A modern social media application that blends the best features of Twitter and TikTok. Share short text posts called "Chars" with optional video content, interact with real-time likes and reposts, and engage with the community through comments.

## 🌟 Features

- **Real-time Social Feed**: Live updates using Socket.IO
- **Char Posts**: Share thoughts with text (280 characters) and optional video
- **Interactive Engagement**: Like, repost, and comment on posts
- **User Profiles**: View user profiles with their post history
- **Video Support**: Upload and share videos with your chars
- **Responsive Design**: Works seamlessly on desktop and mobile

## 🛠 Tech Stack

### Backend
- **Node.js** with Express.js
- **MongoDB** with Mongoose
- **Socket.IO** for real-time features
- **JWT** for authentication
- **Cloudinary** for video storage
- **Multer** for file uploads

### Frontend
- **React** with React Router
- **TailwindCSS** for styling
- **Axios** for API calls
- **Socket.IO Client** for real-time updates

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local or cloud instance)
- Cloudinary account (for video uploads)

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/charotte.git
cd charotte
```

### 2. Setup Backend
```bash
cd server
npm install
cp .env.example .env
# Edit .env with your configuration
npm run dev
```

### 3. Setup Frontend
```bash
cd ../client
npm install
cp .env.example .env
# Edit .env with your API URL
npm start
```

### 4. Environment Configuration

#### Server (.env)
```env
MONGO_URI=mongodb://localhost:27017/charotte
JWT_SECRET=your_jwt_secret_key
CLOUDINARY_NAME=your_cloudinary_name
CLOUDINARY_KEY=your_cloudinary_api_key
CLOUDINARY_SECRET=your_cloudinary_api_secret
PORT=5000
CLIENT_URL=http://localhost:3000
```

#### Client (.env)
```env
REACT_APP_API_URL=http://localhost:5000/api
```

## 📁 Project Structure

```
charotte/
├── server/                 # Backend API
│   ├── controllers/        # Route handlers
│   ├── models/            # MongoDB schemas
│   ├── routes/            # API routes
│   ├── middleware/        # Auth middleware
│   ├── utils/             # Utilities (Cloudinary, Multer)
│   ├── index.js           # Server entry point
│   └── package.json
├── client/                # React frontend
│   ├── src/
│   │   ├── components/    # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API and Socket.IO
│   │   ├── utils/         # Utility functions
│   │   └── App.js
│   ├── public/
│   └── package.json
└── README.md
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user

### Users
- `GET /api/users/:username` - Get user profile

### Chars
- `GET /api/chars` - Get all chars
- `POST /api/chars` - Create new char
- `POST /api/chars/:id/like` - Like/unlike char
- `POST /api/chars/:id/repost` - Repost/unrepost char
- `GET /api/chars/:id/comments` - Get char comments
- `POST /api/chars/:id/comments` - Add comment to char

## 🎯 Usage

1. **Register/Login**: Create an account or sign in
2. **Browse Feed**: View the latest chars from all users
3. **Create Chars**: Share text posts with optional video
4. **Interact**: Like, repost, and comment on chars
5. **Profile**: View your profile and post history
6. **Real-time Updates**: See new posts and interactions instantly

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Protected API routes
- Input validation and sanitization
- CORS configuration

## 🚀 Deployment

### Backend Deployment
1. Set up MongoDB Atlas or your preferred database
2. Configure Cloudinary for production
3. Set environment variables on your hosting platform
4. Deploy to Heroku, Railway, or your preferred service

### Frontend Deployment
1. Build the React app: `npm run build`
2. Deploy to Netlify, Vercel, or your preferred static hosting
3. Update API URL in environment variables

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Inspired by Twitter's simplicity and TikTok's video-first approach
- Built with modern web technologies for optimal performance
- Community-driven development and feedback

---

**Happy Charring!** 🚀
