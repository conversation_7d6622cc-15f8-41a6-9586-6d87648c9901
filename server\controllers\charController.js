const Char = require('../models/Char');

exports.createChar = async (req, res) => {
  try {
    const { content } = req.body;
    const videoUrl = req.file?.path || '';
    
    const char = new Char({ 
      content, 
      videoUrl, 
      author: req.user.id 
    });
    
    await char.save();
    
    const io = req.app.get('io');
    const populatedChar = await char.populate('author', 'username avatarUrl');
    io.emit('char-added', populatedChar);
    
    res.json(char);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: 'Server error' });
  }
};

exports.getChars = async (req, res) => {
  try {
    const chars = await Char.find()
      .sort('-createdAt')
      .populate('author', 'username avatarUrl')
      .lean();
    
    chars.forEach(c => {
      c.likeCount = c.likes.length;
      c.repostCount = c.reposts.length;
      c.likedByMe = c.likes.some(u => u.equals(req.user.id));
      c.repostedByMe = c.reposts.some(u => u.equals(req.user.id));
    });
    
    res.json(chars);
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: 'Server error' });
  }
};

exports.likeChar = async (req, res) => {
  try {
    const char = await Char.findById(req.params.id);
    const me = req.user.id;
    
    if (char.likes.includes(me)) {
      char.likes.pull(me);
    } else {
      char.likes.push(me);
    }
    
    await char.save();
    
    req.app.get('io').emit('char-liked', { 
      charId: char._id, 
      likeCount: char.likes.length 
    });
    
    res.json({ likeCount: char.likes.length });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: 'Server error' });
  }
};

exports.repostChar = async (req, res) => {
  try {
    const char = await Char.findById(req.params.id);
    const me = req.user.id;
    
    if (char.reposts.includes(me)) {
      char.reposts.pull(me);
    } else {
      char.reposts.push(me);
    }
    
    await char.save();
    
    req.app.get('io').emit('char-reposted', { 
      charId: char._id, 
      repostCount: char.reposts.length 
    });
    
    res.json({ repostCount: char.reposts.length });
  } catch (error) {
    console.error(error);
    res.status(500).json({ msg: 'Server error' });
  }
};
