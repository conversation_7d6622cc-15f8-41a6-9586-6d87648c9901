import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import CharCard from '../components/CharCard';

const UserProfile = ({ currentUser }) => {
  const { username } = useParams();
  const [profile, setProfile] = useState(null);
  const [chars, setChars] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadProfile();
  }, [username]);

  const loadProfile = async () => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/users/${username}`
      );
      setProfile(response.data.user);
      setChars(response.data.chars);
    } catch (error) {
      setError('User not found');
      console.error('Error loading profile:', error);
    }
    setLoading(false);
  };

  const handleCharUpdate = (charId, updates) => {
    setChars(prevChars => 
      prevChars.map(char => 
        char._id === charId ? { ...char, ...updates } : char
      )
    );
  };

  if (loading) {
    return (
      <div className="max-w-2xl mx-auto p-4">
        <div className="text-center py-8">Loading profile...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-4">
      {/* Profile Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-20 h-20 bg-primary-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
            {profile.username[0].toUpperCase()}
          </div>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">
              @{profile.username}
            </h1>
            {profile.bio && (
              <p className="text-gray-600 mt-2">{profile.bio}</p>
            )}
            <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
              <span>{chars.length} chars</span>
              <span>Joined {new Date(profile.createdAt || Date.now()).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* User's Chars */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {profile.username === currentUser.username ? 'Your Chars' : `${profile.username}'s Chars`}
        </h2>
        
        {chars.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {profile.username === currentUser.username 
                ? "You haven't posted any chars yet" 
                : `${profile.username} hasn't posted any chars yet`
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {chars.map((char) => (
              <CharCard
                key={char._id}
                char={char}
                currentUser={currentUser}
                onUpdate={handleCharUpdate}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserProfile;
