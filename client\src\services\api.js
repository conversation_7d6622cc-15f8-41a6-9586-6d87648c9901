import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
};

// User API calls
export const userAPI = {
  getProfile: (username) => api.get(`/users/${username}`),
};

// Char API calls
export const charAPI = {
  getChars: () => api.get('/chars'),
  createChar: (formData) => api.post('/chars', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  likeChar: (charId) => api.post(`/chars/${charId}/like`),
  repostChar: (charId) => api.post(`/chars/${charId}/repost`),
  getComments: (charId) => api.get(`/chars/${charId}/comments`),
  addComment: (charId, content) => api.post(`/chars/${charId}/comments`, { content }),
};

export default api;
