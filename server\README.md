# CHAROTTE Backend

The backend API server for Charotte social media application built with Node.js, Express, MongoDB, and Socket.IO.

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (local or MongoDB Atlas)
- Cloudinary account for video storage

### Installation

1. **Install Dependencies**
```bash
npm install
```

2. **Environment Setup**
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
MONGO_URI=mongodb://localhost:27017/charotte
JWT_SECRET=your_super_secret_jwt_key
CLOUDINARY_NAME=your_cloudinary_name
CLOUDINARY_KEY=your_cloudinary_api_key
CLOUDINARY_SECRET=your_cloudinary_api_secret
PORT=5000
CLIENT_URL=http://localhost:3000
```

3. **Start Development Server**
```bash
npm run dev
```

The server will run on `http://localhost:5000`

## 📁 Project Structure

```
server/
├── controllers/           # Route handlers
│   ├── authController.js     # Authentication logic
│   ├── userController.js     # User profile logic
│   ├── charController.js     # Char CRUD operations
│   └── commentController.js  # Comment operations
├── models/               # MongoDB schemas
│   ├── User.js              # User model
│   ├── Char.js              # Char model
│   └── Comment.js           # Comment model
├── routes/               # API routes
│   ├── auth.js              # Auth routes
│   ├── users.js             # User routes
│   └── chars.js             # Char routes
├── middleware/           # Custom middleware
│   └── auth.js              # JWT authentication
├── utils/                # Utilities
│   ├── cloudinary.js        # Cloudinary config
│   └── multer.js            # File upload config
├── index.js              # Server entry point
└── package.json
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user

### Users
- `GET /api/users/:username` - Get user profile and chars

### Chars
- `GET /api/chars` - Get all chars (authenticated)
- `POST /api/chars` - Create new char (with optional video)
- `POST /api/chars/:id/like` - Toggle like on char
- `POST /api/chars/:id/repost` - Toggle repost on char

### Comments
- `GET /api/chars/:id/comments` - Get comments for a char
- `POST /api/chars/:id/comments` - Add comment to char

### Health Check
- `GET /api/health` - Server health status

## 🔌 Real-time Features

The server uses Socket.IO for real-time updates:

- **char-added**: New char posted
- **char-liked**: Char liked/unliked
- **char-reposted**: Char reposted/unreposted
- **comment-added**: New comment added

## 🛡 Security

- JWT-based authentication
- Password hashing with bcryptjs
- Protected routes with auth middleware
- CORS configuration
- Input validation

## 🗄 Database Schema

### User
```javascript
{
  username: String (unique, required),
  password: String (required, hashed),
  bio: String (default: ''),
  avatarUrl: String (default: '')
}
```

### Char
```javascript
{
  content: String (max 280 chars),
  videoUrl: String (default: ''),
  author: ObjectId (ref: User, required),
  likes: [ObjectId] (ref: User),
  reposts: [ObjectId] (ref: User),
  createdAt: Date (default: now)
}
```

### Comment
```javascript
{
  content: String (required),
  author: ObjectId (ref: User, required),
  char: ObjectId (ref: Char, required),
  createdAt: Date (default: now)
}
```

## 📦 Dependencies

### Production
- `express` - Web framework
- `mongoose` - MongoDB ODM
- `socket.io` - Real-time communication
- `jsonwebtoken` - JWT authentication
- `bcryptjs` - Password hashing
- `multer` - File upload handling
- `cloudinary` - Video storage
- `cors` - Cross-origin requests
- `dotenv` - Environment variables

### Development
- `nodemon` - Development server with auto-restart

## 🚀 Deployment

### Environment Variables for Production
```env
MONGO_URI=mongodb+srv://username:<EMAIL>/charotte
JWT_SECRET=your_production_jwt_secret
CLOUDINARY_NAME=your_cloudinary_name
CLOUDINARY_KEY=your_cloudinary_api_key
CLOUDINARY_SECRET=your_cloudinary_api_secret
PORT=5000
CLIENT_URL=https://your-frontend-domain.com
NODE_ENV=production
```

### Deploy to Heroku
```bash
# Install Heroku CLI and login
heroku create your-app-name
heroku config:set MONGO_URI=your_mongodb_uri
heroku config:set JWT_SECRET=your_jwt_secret
# ... set other environment variables
git push heroku main
```

## 🧪 Testing

```bash
# Run tests (when implemented)
npm test

# Check server health
curl http://localhost:5000/api/health
```

---

Built with ❤️ for the Charotte community!
