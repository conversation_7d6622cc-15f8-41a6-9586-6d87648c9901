import React, { useState, useEffect } from 'react';
import axios from 'axios';
import CharCard from '../components/CharCard';
import { initSocket, getSocket } from '../services/socket';

const HomePage = ({ user }) => {
  const [chars, setChars] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadChars();
    
    // Initialize socket connection
    initSocket();
    const socket = getSocket();
    
    // Listen for real-time updates
    socket.on('char-added', (newChar) => {
      setChars(prevChars => [newChar, ...prevChars]);
    });

    socket.on('char-liked', ({ charId, likeCount }) => {
      setChars(prevChars => 
        prevChars.map(char => 
          char._id === charId 
            ? { ...char, likeCount, likedByMe: !char.likedByMe }
            : char
        )
      );
    });

    socket.on('char-reposted', ({ charId, repostCount }) => {
      setChars(prevChars => 
        prevChars.map(char => 
          char._id === charId 
            ? { ...char, repostCount, repostedByMe: !char.repostedByMe }
            : char
        )
      );
    });

    return () => {
      socket.off('char-added');
      socket.off('char-liked');
      socket.off('char-reposted');
    };
  }, []);

  const loadChars = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/chars`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setChars(response.data);
    } catch (error) {
      setError('Failed to load chars');
      console.error('Error loading chars:', error);
    }
    setLoading(false);
  };

  const handleCharUpdate = (charId, updates) => {
    setChars(prevChars => 
      prevChars.map(char => 
        char._id === charId ? { ...char, ...updates } : char
      )
    );
  };

  if (loading) {
    return (
      <div className="max-w-2xl mx-auto p-4">
        <div className="text-center py-8">Loading chars...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome back, @{user.username}!
        </h1>
        <p className="text-gray-600">
          Check out the latest chars from the community
        </p>
      </div>

      {chars.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No chars yet!</p>
          <p className="text-gray-400">Be the first to share something</p>
        </div>
      ) : (
        <div className="space-y-4">
          {chars.map((char) => (
            <CharCard
              key={char._id}
              char={char}
              currentUser={user}
              onUpdate={handleCharUpdate}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default HomePage;
