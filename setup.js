#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up Charotte...\n');

// Function to run commands
const runCommand = (command, cwd = process.cwd()) => {
  try {
    console.log(`Running: ${command}`);
    execSync(command, { cwd, stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`Error running command: ${command}`);
    console.error(error.message);
    return false;
  }
};

// Function to copy env files
const setupEnvFiles = () => {
  console.log('\n📝 Setting up environment files...');
  
  // Server .env
  const serverEnvExample = path.join(__dirname, 'server', '.env.example');
  const serverEnv = path.join(__dirname, 'server', '.env');
  
  if (fs.existsSync(serverEnvExample) && !fs.existsSync(serverEnv)) {
    fs.copyFileSync(serverEnvExample, serverEnv);
    console.log('✅ Created server/.env from .env.example');
  }
  
  // Client .env
  const clientEnvExample = path.join(__dirname, 'client', '.env.example');
  const clientEnv = path.join(__dirname, 'client', '.env');
  
  if (fs.existsSync(clientEnvExample) && !fs.existsSync(clientEnv)) {
    fs.copyFileSync(clientEnvExample, clientEnv);
    console.log('✅ Created client/.env from .env.example');
  }
};

// Main setup function
const setup = async () => {
  try {
    // Setup environment files
    setupEnvFiles();
    
    // Install server dependencies
    console.log('\n📦 Installing server dependencies...');
    const serverPath = path.join(__dirname, 'server');
    if (!runCommand('npm install', serverPath)) {
      throw new Error('Failed to install server dependencies');
    }
    
    // Install client dependencies
    console.log('\n📦 Installing client dependencies...');
    const clientPath = path.join(__dirname, 'client');
    if (!runCommand('npm install', clientPath)) {
      throw new Error('Failed to install client dependencies');
    }
    
    console.log('\n🎉 Setup complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Configure your environment variables:');
    console.log('   - Edit server/.env with your MongoDB URI and other settings');
    console.log('   - Edit client/.env if needed');
    console.log('\n2. Start the development servers:');
    console.log('   - Server: cd server && npm run dev');
    console.log('   - Client: cd client && npm start');
    console.log('\n3. Open http://localhost:3000 in your browser');
    console.log('\n📚 For more information, check the README.md files');
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
};

// Run setup
setup();
