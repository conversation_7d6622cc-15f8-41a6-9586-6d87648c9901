const router = require('express').Router();
const auth = require('../middleware/auth');
const upload = require('../utils/multer');
const {
  createChar, 
  getChars, 
  likeChar, 
  repostChar
} = require('../controllers/charController');
const { 
  addComment, 
  getComments 
} = require('../controllers/commentController');

router.post('/', auth, upload.single('video'), createChar);
router.get('/', auth, getChars);
router.post('/:id/like', auth, likeChar);
router.post('/:id/repost', auth, repostChar);
router.post('/:id/comments', auth, addComment);
router.get('/:id/comments', auth, getComments);

module.exports = router;
