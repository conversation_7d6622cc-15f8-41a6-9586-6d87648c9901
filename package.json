{"name": "charotte", "version": "1.0.0", "description": "A modern social media application blending Twitter and TikTok features", "main": "setup.js", "scripts": {"setup": "node setup.js", "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm start", "server:start": "cd server && npm start", "client:build": "cd client && npm run build", "install:server": "cd server && npm install", "install:client": "cd client && npm install", "install:all": "npm run install:server && npm run install:client", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["social-media", "react", "nodejs", "mongodb", "socket.io", "express", "mern"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/charotte.git"}, "bugs": {"url": "https://github.com/your-username/charotte/issues"}, "homepage": "https://github.com/your-username/charotte#readme"}