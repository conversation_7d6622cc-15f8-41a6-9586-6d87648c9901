import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const UploadPage = ({ user }) => {
  const [formData, setFormData] = useState({
    content: '',
    video: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [preview, setPreview] = useState(null);
  const navigate = useNavigate();

  const handleChange = (e) => {
    if (e.target.name === 'video') {
      const file = e.target.files[0];
      setFormData({ ...formData, video: file });
      
      if (file) {
        const url = URL.createObjectURL(file);
        setPreview(url);
      } else {
        setPreview(null);
      }
    } else {
      setFormData({
        ...formData,
        [e.target.name]: e.target.value
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.content.trim() && !formData.video) {
      setError('Please add some content or upload a video');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const token = localStorage.getItem('token');
      const submitData = new FormData();
      
      if (formData.content.trim()) {
        submitData.append('content', formData.content);
      }
      
      if (formData.video) {
        submitData.append('video', formData.video);
      }

      await axios.post(
        `${process.env.REACT_APP_API_URL}/chars`,
        submitData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      navigate('/');
    } catch (error) {
      setError(error.response?.data?.msg || 'Failed to create char');
    }
    setLoading(false);
  };

  const removeVideo = () => {
    setFormData({ ...formData, video: null });
    setPreview(null);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Create a New Char</h1>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
              What's on your mind?
            </label>
            <textarea
              id="content"
              name="content"
              rows={4}
              maxLength={280}
              value={formData.content}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Share your thoughts... (max 280 characters)"
            />
            <div className="text-right text-sm text-gray-500 mt-1">
              {formData.content.length}/280
            </div>
          </div>

          <div>
            <label htmlFor="video" className="block text-sm font-medium text-gray-700 mb-2">
              Upload Video (Optional)
            </label>
            <input
              id="video"
              name="video"
              type="file"
              accept="video/*"
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          {preview && (
            <div className="relative">
              <video
                src={preview}
                controls
                className="w-full max-h-96 rounded-lg"
              />
              <button
                type="button"
                onClick={removeVideo}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600"
              >
                ×
              </button>
            </div>
          )}

          <div className="flex justify-between items-center">
            <button
              type="button"
              onClick={() => navigate('/')}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            
            <button
              type="submit"
              disabled={loading || (!formData.content.trim() && !formData.video)}
              className="bg-primary-500 text-white px-6 py-2 rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Posting...' : 'Post Char'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UploadPage;
