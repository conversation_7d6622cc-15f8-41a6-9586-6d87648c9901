# Deployment Guide for Charotte

This guide covers deploying Charotte to various platforms.

## 🚀 Quick Deploy Options

### Option 1: <PERSON><PERSON> (Recommended for beginners)

#### Backend Deployment
1. **Prepare for deployment**
```bash
cd server
heroku create your-app-name-api
```

2. **Set environment variables**
```bash
heroku config:set MONGO_URI=your_mongodb_atlas_uri
heroku config:set JWT_SECRET=your_super_secret_jwt_key
heroku config:set CLOUDINARY_NAME=your_cloudinary_name
heroku config:set CLOUDINARY_KEY=your_cloudinary_api_key
heroku config:set CLOUDINARY_SECRET=your_cloudinary_api_secret
heroku config:set CLIENT_URL=https://your-frontend-domain.netlify.app
heroku config:set NODE_ENV=production
```

3. **Deploy**
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

#### Frontend Deployment (Netlify)
1. **Build the app**
```bash
cd client
npm run build
```

2. **Deploy to Netlify**
- Drag and drop the `build` folder to Netlify
- Or connect your GitHub repo to Netlify for automatic deployments

3. **Set environment variables in Netlify**
```
REACT_APP_API_URL=https://your-app-name-api.herokuapp.com/api
```

### Option 2: Railway

#### Backend
1. **Connect your GitHub repo to Railway**
2. **Set environment variables in Railway dashboard**
3. **Deploy automatically on push**

#### Frontend
1. **Deploy to Vercel or Netlify**
2. **Set environment variables**

### Option 3: DigitalOcean App Platform

1. **Create new app from GitHub repo**
2. **Configure build settings**
3. **Set environment variables**
4. **Deploy**

## 🗄 Database Setup

### MongoDB Atlas (Recommended)
1. **Create account at MongoDB Atlas**
2. **Create new cluster**
3. **Create database user**
4. **Whitelist IP addresses (0.0.0.0/0 for all)**
5. **Get connection string**

### Local MongoDB (Development)
```bash
# Install MongoDB
brew install mongodb/brew/mongodb-community

# Start MongoDB
brew services start mongodb/brew/mongodb-community

# Connection string
MONGO_URI=mongodb://localhost:27017/charotte
```

## ☁️ Cloudinary Setup

1. **Create account at Cloudinary**
2. **Get your credentials from dashboard**
3. **Set environment variables**

## 🔧 Environment Variables

### Server (.env)
```env
# Database
MONGO_URI=mongodb+srv://username:<EMAIL>/charotte

# Authentication
JWT_SECRET=your_super_secret_jwt_key_minimum_32_characters

# Cloudinary
CLOUDINARY_NAME=your_cloudinary_name
CLOUDINARY_KEY=your_cloudinary_api_key
CLOUDINARY_SECRET=your_cloudinary_api_secret

# Server
PORT=5000
NODE_ENV=production
CLIENT_URL=https://your-frontend-domain.com
```

### Client (.env)
```env
REACT_APP_API_URL=https://your-backend-domain.com/api
```

## 🔒 Security Checklist

- [ ] Use strong JWT secret (minimum 32 characters)
- [ ] Set up CORS properly
- [ ] Use HTTPS in production
- [ ] Set up rate limiting
- [ ] Validate all inputs
- [ ] Use environment variables for secrets
- [ ] Set up monitoring and logging

## 📊 Monitoring

### Error Tracking
- Sentry for error tracking
- LogRocket for session replay

### Performance Monitoring
- New Relic or DataDog
- MongoDB Atlas monitoring

### Uptime Monitoring
- Pingdom or UptimeRobot

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
    
    - name: Install dependencies
      run: |
        cd server && npm install
        cd ../client && npm install
    
    - name: Run tests
      run: |
        cd server && npm test
        cd ../client && npm test
    
    - name: Deploy to Heroku
      uses: akhileshns/heroku-deploy@v3.12.12
      with:
        heroku_api_key: ${{secrets.HEROKU_API_KEY}}
        heroku_app_name: "your-app-name"
        heroku_email: "<EMAIL>"
```

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check CLIENT_URL environment variable
   - Verify CORS configuration in server

2. **Database Connection Issues**
   - Check MongoDB URI format
   - Verify network access in MongoDB Atlas

3. **File Upload Issues**
   - Verify Cloudinary credentials
   - Check file size limits

4. **Socket.IO Connection Issues**
   - Ensure WebSocket support on hosting platform
   - Check CORS configuration for Socket.IO

### Logs and Debugging
```bash
# Heroku logs
heroku logs --tail -a your-app-name

# Railway logs
railway logs

# Local debugging
DEBUG=* npm run dev
```

## 📈 Scaling Considerations

### Database
- Use MongoDB Atlas for automatic scaling
- Implement database indexing
- Consider read replicas for high traffic

### File Storage
- Cloudinary handles scaling automatically
- Consider CDN for global distribution

### Server
- Use horizontal scaling (multiple instances)
- Implement load balancing
- Use Redis for session storage in multi-instance setup

---

Need help? Check the main README.md or create an issue on GitHub!
